package com.example.ai.feign;

import com.example.ai.dto.ChatResponse;
import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.result.Result;
import com.example.common.entity.vo.AiChatSessionVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * BusinessFeign简单降级实现
 * 当business-service服务不可用时提供默认响应
 */
@Slf4j
@Component
public class BusinessFeignFallback implements BusinessFeign {

    @Override
    public Result<AiChatSessionVo> createConversation(AiChatSessionDto aiChatSessionDto) {
        log.warn("🔥 BusinessFeign.createConversation 调用失败，执行降级逻辑");

        // 记录详细错误信息到日志
        String errorMessage = "创建会话服务暂时不可用，请稍后重试";
        String detailMessage = "business-service服务不可用，无法创建会话";
        log.error("Feign调用失败: {}", detailMessage);

        // 返回用户友好的错误信息
        return Result.error(503, errorMessage);
    }

    @Override
    public Result updateConversation(AiChatSessionDto aiChatSessionDto) {
        return null;
    }

    @Override
    public List<ChatResponse> getConversations() {
        log.warn("🔥 BusinessFeign.getConversations 调用失败，执行降级逻辑");

        // 记录详细错误信息到日志
        String detailMessage = "business-service服务不可用，无法获取会话列表";
        log.error("Feign调用失败: {}", detailMessage);

        // 对于列表查询，返回空列表是合理的降级策略
        // 但我们需要在上层服务中处理这种情况并给用户提示
        return new ArrayList<>();
    }
}

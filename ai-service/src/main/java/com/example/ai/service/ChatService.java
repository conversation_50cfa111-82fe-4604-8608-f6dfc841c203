package com.example.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.Chat;
import com.example.common.entity.vo.AiChatSessionVo;
import jakarta.validation.constraints.NotBlank;

import java.util.List;


public interface ChatService extends IService<Chat> {
    /**
     * 创建新对话
     * @param chatRequest
     * @return
     */
    AiChatSessionVo createConversation(ChatRequest chatRequest);

    /**
     * 获取会话列表

     * @return
     */
    List<ChatResponse> getConversations();

    /**
     * 更新会话标题
     * @param conversationId
     * @param title
     */
    void updateConversationTitle(String conversationId, @NotBlank(message = "消息内容不能为空") String title);
}

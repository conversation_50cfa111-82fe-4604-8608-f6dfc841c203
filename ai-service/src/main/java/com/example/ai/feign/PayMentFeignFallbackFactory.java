package com.example.ai.feign;

import com.example.common.entity.vo.PaymentMethodVo;
import com.example.common.entity.vo.VipPlanVo;
import com.example.common.result.Result;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * PayMentFeign降级工厂类
 * 当payment-service服务不可用时提供降级响应
 * 使用FallbackFactory可以获取到异常信息，便于分析问题
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@Component
public class PayMentFeignFallbackFactory implements FallbackFactory<PayMentFeign> {

    @Override
    public PayMentFeign create(Throwable cause) {
        return new PayMentFeign() {
            
            @Override
            public Result<List<VipPlanVo>> getAllVipPlansFeatures() {
                log.error("🔥 PayMentFeign.getAllVipPlansFeatures 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取VIP套餐列表失败: {}", errorMessage);
                
                return Result.error(503, "VIP套餐服务暂时不可用: " + errorMessage);
            }

            @Override
            public Result<List<PaymentMethodVo>> paymentMethodList() {
                log.error("🔥 PayMentFeign.paymentMethodList 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取支付方式列表失败: {}", errorMessage);
                
                return Result.error(503, "支付方式服务暂时不可用: " + errorMessage);
            }

            @Override
            public Result getRechargeList() {
                log.error("🔥 PayMentFeign.getRechargeList 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取充值列表失败: {}", errorMessage);
                
                return Result.error(503, "充值服务暂时不可用: " + errorMessage);
            }

            @Override
            public Result getAliPayCode() {
                log.error("🔥 PayMentFeign.getAliPayCode 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取支付宝支付码失败: {}", errorMessage);
                
                return Result.error(503, "支付宝支付服务暂时不可用: " + errorMessage);
            }

            @Override
            public Result getOrderNum() {
                log.error("🔥 PayMentFeign.getOrderNum 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取订单号失败: {}", errorMessage);
                
                return Result.error(503, "订单服务暂时不可用: " + errorMessage);
            }

            @Override
            public Result getPagePayCode(String payType) {
                log.error("🔥 PayMentFeign.getPagePayCode 调用失败，执行降级逻辑", cause);
                
                String errorMessage = extractErrorMessage(cause);
                log.error("获取支付页面失败: {}", errorMessage);
                
                return Result.error(503, "支付页面服务暂时不可用: " + errorMessage);
            }
        };
    }

    /**
     * 从异常中提取错误信息
     * 
     * @param cause 异常对象
     * @return 错误信息
     */
    private String extractErrorMessage(Throwable cause) {
        if (cause == null) {
            return "未知错误";
        }
        
        // 如果是FeignException，尝试提取服务端返回的错误信息
        if (cause instanceof FeignException) {
            FeignException feignException = (FeignException) cause;
            String responseBody = feignException.contentUTF8();
            
            // 记录详细的Feign异常信息
            log.error("Feign异常详情 - 状态码: {}, 响应体: {}", 
                     feignException.status(), responseBody);
            
            // 如果响应体不为空，返回响应体内容
            if (responseBody != null && !responseBody.trim().isEmpty()) {
                return responseBody;
            }
            
            // 否则返回状态码和原因
            return String.format("HTTP %d: %s", feignException.status(), feignException.getMessage());
        }
        
        // 其他异常直接返回异常信息
        return cause.getMessage() != null ? cause.getMessage() : cause.getClass().getSimpleName();
    }
}

package com.example.business.controller;

import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.business.service.IAiChatMessageService;
import com.example.business.service.IAiChatSessionService;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/ai/chatMessage")
@RequiredArgsConstructor
public class AiChatMessageController {
    private final IAiChatMessageService aiChatMessageService;
    private final IAiChatSessionService aiChatSessionService;


    /***
     * 创建新对话
     * @param aiChatSessionDto
     * @return
     */
    @PostMapping("/createConversation")
    public  Result<AiChatSessionVo> createConversation(@RequestBody @Validated AiChatSessionDto aiChatSessionDto) {
        return Result.success(aiChatSessionService.insertAiChatSession(aiChatSessionDto));
    }

    /**
     * 更新对话标题
     * @param aiChatSessionDto
     * @return
     */
    @PutMapping("/updateConversation")
    public Result updateConversation(@RequestBody @Validated AiChatSessionDto aiChatSessionDto) {
        return Result.success(aiChatSessionService.updateAiChatSession(aiChatSessionDto));
    }
}

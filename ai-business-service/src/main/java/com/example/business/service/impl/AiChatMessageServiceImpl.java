package com.example.business.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiChatMessageMapper;
import com.example.business.entity.AiChatMessage;
import com.example.business.service.IAiChatMessageService;

/**
 * AI聊天消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@RequiredArgsConstructor
public class AiChatMessageServiceImpl extends ServiceImpl<AiChatMessageMapper,AiChatMessage> implements IAiChatMessageService
{
  
    private final AiChatMessageMapper aiChatMessageMapper;

    /**
     * 查询AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return AI聊天消息
     */
    @Override
    public AiChatMessage selectAiChatMessageById(Long id)
    {
        return aiChatMessageMapper.selectAiChatMessageById(id);
    }

    /**
     * 查询AI聊天消息列表
     *
     * @param aiChatMessage AI聊天消息
     * @return AI聊天消息
     */
    @Override
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessage aiChatMessage)
    {
        return aiChatMessageMapper.selectAiChatMessageList(aiChatMessage);
    }

    /**
     * 新增AI聊天消息
     *
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    @Override
    public int insertAiChatMessage(AiChatMessage aiChatMessage)
    {
        aiChatMessage.setCreateTime(DateUtils.getNowDate());
        return aiChatMessageMapper.insertAiChatMessage(aiChatMessage);
    }

    /**
     * 修改AI聊天消息
     *
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    @Override
    public int updateAiChatMessage(AiChatMessage aiChatMessage)
    {
        aiChatMessage.setUpdateTime(DateUtils.getNowDate());
        return aiChatMessageMapper.updateAiChatMessage(aiChatMessage);
    }

    /**
     * 批量删除AI聊天消息
     *
     * @param ids 需要删除的AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageByIds(Long[] ids)
    {
        return aiChatMessageMapper.deleteAiChatMessageByIds(ids);
    }

    /**
     * 删除AI聊天消息信息
     *
     * @param id AI聊天消息主键
     * @return 结果
     */
    @Override
    public int deleteAiChatMessageById(Long id)
    {
        return aiChatMessageMapper.deleteAiChatMessageById(id);
    }
}

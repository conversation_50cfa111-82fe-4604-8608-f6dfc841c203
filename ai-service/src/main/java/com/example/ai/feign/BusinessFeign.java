package com.example.ai.feign;

import com.example.common.entity.dto.AiChatSessionDto;
import com.example.common.entity.vo.AiChatSessionVo;
import com.example.ai.dto.ChatResponse;
import com.example.common.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Business服务Feign客户端
 * 用于调用ai-business-service的相关接口
 * 配置了FallbackFactory以支持降级功能
 * 修复版本 - 使用FallbackFactory确保降级功能正确工作
 */
@FeignClient(
    value = "business-service",
    fallbackFactory = BusinessFeignFallback.class
)
public interface BusinessFeign {

    /**
     * 创建新对话会话
     * 调用ai-business-service的/ai/chatMessage/createConversation接口
     *
     * @param aiChatSessionDto 会话创建请求参数
     * @return 创建结果，包装在Result中
     */
    @PostMapping("/ai/chatMessage/createConversation")
    Result<AiChatSessionVo> createConversation(@RequestBody AiChatSessionDto aiChatSessionDto);

    /**
     * 更新对话标题
     * 调用ai-business-service的/ai/chatMessage/updateConversation接口
     *
     * @param aiChatSessionDto 会话创建请求参数
     * @return 创建结果，包装在Result中
     */
    @PutMapping("/ai/chatMessage/updateConversation")
    Result updateConversation(@RequestBody AiChatSessionDto aiChatSessionDto);

    /**
     * 获取对话列表
     * 调用ai-business-service的/business/conversations接口
     *
     * @return 对话列表
     */
    @GetMapping("/business/conversations")
    List<ChatResponse> getConversations();
}

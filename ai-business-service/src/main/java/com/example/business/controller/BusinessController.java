package com.example.business.controller;


import com.example.business.entity.dto.ChatInfoResponse;
import com.example.business.entity.dto.ChatRequest;

import com.example.business.entity.dto.ChatResponse;
import com.example.business.service.ChatInfoService;
import com.example.business.service.ChatService;
import com.example.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * AI控制器
 */
@RestController
@RequestMapping("/business")
@RequiredArgsConstructor
public class BusinessController {


    private final ChatService chatService;

    private final ChatInfoService chatInfoService;

//    /**
//     * 单轮对话文本输出(OpenAI)
//     *
//     * @param message
//     * @return
//     */
//    @GetMapping("/singleChatCompletionText")
//    public Result singleChatCompletionTest(String message, String chatId) {
//        return Result.success(aiService.singleChatCompletionText(message, chatId));
//    }
//
//    /**
//     * 单轮对话json输出 (DashScope)
//     *
//     * @param message
//     * @return
//     */
//    @GetMapping("/singleGenerationResultJson")
//    public Result singleGenerationResultJson(String message) {
//        return Result.success(aiService.singleGenerationResultJson(message));
//    }
//
//    /**
//     * 多轮对话 (DashScope)
//     *
//     * @param message
//     * @return
//     */
//    @GetMapping("/multiwheelGenerationResultJson")
//    public Result multiwheelGenerationResultJson(String message, Integer type, String chatId) {
//        return Result.success(aiService.multiwheelGenerationResultJson(message, type, chatId));
//    }

    /***
     * 创建新对话
     * @param chatRequest
     * @return
     */
    @PostMapping("/createConversation")
    public Result createConversation(@RequestBody @Validated ChatRequest chatRequest) {

        return Result.success(chatService.createConversation(chatRequest));
    }

    /**
     * 获取对话列表
     *
     * @return
     */
    @GetMapping("/conversations")
    public List<ChatResponse> getConversations() {
        return chatService.getConversations();
    }


    /**
     * 更新对话标题
     *
     * @param conversationId
     * @return
     */
    @PutMapping("/updateConversation/{conversationId}")
    public Result updateConversation(@PathVariable String conversationId, @RequestBody ChatRequest chatRequest) {
        chatService.updateConversationTitle(conversationId, chatRequest.getTitle());
        return Result.success();
    }

    /**
     * 根据会话id获取会话详细列表信息
     *
     * @param chatId
     * @return
     */
    @GetMapping("/chatInfo/{chatId}/messages")
    public Result chatInfo(@PathVariable String chatId) {
        List<ChatInfoResponse> chatInfoResponseList = chatInfoService.getByChatId(chatId);
        return Result.success(chatInfoResponseList);
    }


}

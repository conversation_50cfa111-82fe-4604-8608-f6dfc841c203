package com.example.payment.controller;

import com.example.common.result.Result;
import com.example.payment.factory.impl.AliPayImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 * 用于测试支付功能和异常处理机制
 *
 * <AUTHOR>
 * Create by 2025/7/30 15:31
 */
@Slf4j
@RestController
@RequestMapping("/test")
@RequiredArgsConstructor
public class TestController {
    private final AliPayImpl aliPay;

    @GetMapping("/test1")
    public Result test1(){
        return Result.success(aliPay.payStrategyCode());
    }

    /**
     * 测试正常响应
     */
    @GetMapping("/success")
    public Result<String> testSuccess() {
        log.info("测试成功响应");
        return Result.success("测试成功");
    }

    /**
     * 测试业务异常
     */
    @GetMapping("/business-error")
    public Result<String> testBusinessError() {
        log.info("测试业务异常");
        return Result.error(400, "这是一个业务异常测试");
    }

    /**
     * 测试运行时异常
     */
    @GetMapping("/runtime-error")
    public Result<String> testRuntimeError() {
        log.info("测试运行时异常");
        throw new RuntimeException("这是一个运行时异常测试");
    }

    /**
     * 测试自定义异常
     */
    @GetMapping("/custom-error")
    public Result<String> testCustomError(@RequestParam(defaultValue = "false") boolean throwError) {
        log.info("测试自定义异常，throwError={}", throwError);

        if (throwError) {
            throw new RuntimeException("支付服务内部错误：余额不足");
        }

        return Result.success("支付成功");
    }

    /**
     * 模拟支付异常
     */
    @GetMapping("/payment-error")
    public Result<String> testPaymentError() {
        log.info("模拟支付异常");
        throw new RuntimeException("暂无其他支付方式");
    }
}

package com.example.business.service.impl;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.business.entity.Chat;
import com.example.business.entity.dto.ChatResponse;
import com.example.common.annotation.OperationLog;
import com.example.common.entity.Enum.OperationLogEnum;
import com.example.common.entity.dto.AiChatSessionDto;

import com.example.common.entity.vo.AiChatSessionVo;
import com.example.common.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import com.example.business.mapper.AiChatSessionMapper;
import com.example.business.entity.AiChatSession;
import com.example.business.service.IAiChatSessionService;
import org.springframework.transaction.annotation.Transactional;

/**
 * AI聊天会话Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiChatSessionServiceImpl  extends ServiceImpl<AiChatSessionMapper,AiChatSession> implements IAiChatSessionService
{

    private final AiChatSessionMapper aiChatSessionMapper;

    /**
     * 查询AI聊天会话
     *
     * @param id AI聊天会话主键
     * @return AI聊天会话
     */
    @Override
    public AiChatSession selectAiChatSessionById(Long id)
    {
        return aiChatSessionMapper.selectAiChatSessionById(id);
    }

    /**
     * 查询AI聊天会话列表
     *
     * @param aiChatSession AI聊天会话
     * @return AI聊天会话
     */
    @Override
    public List<AiChatSession> selectAiChatSessionList(AiChatSession aiChatSession)
    {
        return aiChatSessionMapper.selectAiChatSessionList(aiChatSession);
    }

    /**
     * 新增AI聊天会话
     *
     * @param aiChatSessionDto AI聊天会话
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(
        operationType = OperationLogEnum.operation_type_chat,
        operationDesc = "用户发起聊天请求",
        recordRequest = true,
        recordResponse = true,
        recordExecutionTime = true
    ) //异步记录日志
    public AiChatSessionVo insertAiChatSession(AiChatSessionDto aiChatSessionDto)
    {
        try {
            //生成会话id
            String chatId = UUID.randomUUID().toString();
            //构建会话对象
            AiChatSession aiChatSession = AiChatSession.builder().sessionId(chatId)
                    .title(aiChatSessionDto.getTitle())
                    .userId(aiChatSessionDto.getUserId()).build();

            // 保存到数据库
            boolean insertResult = this.save(aiChatSession);
            if (!insertResult) {
                throw new RuntimeException("会话创建失败，数据库插入异常");
            }

            log.info("新建会话创建成功，chatId: {}, insertResult: {}", chatId, insertResult);

            // 构建响应对象
            AiChatSessionVo chatSessionVo = AiChatSessionVo.builder()
                    .id(chatId)
                    .title(aiChatSessionDto.getTitle())
                    .createdAt(new Date()).build();

            log.info("会话响应构建完成，chatId: {}", chatId);

            return chatSessionVo;

        } catch (Exception e) {
            log.error("创建会话失败，title: {}, userId: {}, error: {}",
                    aiChatSessionDto.getTitle(), aiChatSessionDto.getUserId(), e.getMessage(), e);
            throw new RuntimeException("创建会话失败: " + e.getMessage(), e);
        }
    }

    /**
     * 修改AI聊天会话
     *
     * @param aiChatSessionDto AI聊天会话
     * @return 结果
     */
    @Override
    @OperationLog(
        operationType = OperationLogEnum.operation_type_config_change,
        operationDesc = "修改AI聊天会话配置",
        recordRequest = true,
        recordResponse = true,
        recordExecutionTime = true
    )
    public int updateAiChatSession(AiChatSessionDto aiChatSessionDto)
    {
        if(StringUtils.isBlank(aiChatSessionDto.getSessionId())){
            throw new IllegalArgumentException("会话ID不能为空");
        }
        LambdaUpdateWrapper<AiChatSession> aiChatSessionLambdaUpdateWrapper = new LambdaUpdateWrapper<AiChatSession>()
                .eq(AiChatSession::getSessionId, aiChatSessionDto.getSessionId())
                .set(AiChatSession::getTitle, aiChatSessionDto.getTitle());
        int update = aiChatSessionMapper.update(aiChatSessionLambdaUpdateWrapper);
        if (update == 0) {
            throw new RuntimeException("会话更新失败，会话可能不存在");
        }
        return update;
    }

    /**
     * 批量删除AI聊天会话
     *
     * @param ids 需要删除的AI聊天会话主键
     * @return 结果
     */
    @Override
    public int deleteAiChatSessionByIds(Long[] ids)
    {
        return aiChatSessionMapper.deleteAiChatSessionByIds(ids);
    }

    /**
     * 删除AI聊天会话信息
     *
     * @param id AI聊天会话主键
     * @return 结果
     */
    @Override
    public int deleteAiChatSessionById(Long id)
    {
        return aiChatSessionMapper.deleteAiChatSessionById(id);
    }
}

package com.example.business.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.business.entity.AiChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

/**
 * AI聊天消息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-16
 */
@Mapper
@Repository
public interface AiChatMessageMapper extends BaseMapper<AiChatMessage>
{
    /**
     * 查询AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return AI聊天消息
     */
    public AiChatMessage selectAiChatMessageById(Long id);

    /**
     * 查询AI聊天消息列表
     *
     * @param aiChatMessage AI聊天消息
     * @return AI聊天消息集合
     */
    public List<AiChatMessage> selectAiChatMessageList(AiChatMessage aiChatMessage);

    /**
     * 新增AI聊天消息
     *
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int insertAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 修改AI聊天消息
     *
     * @param aiChatMessage AI聊天消息
     * @return 结果
     */
    public int updateAiChatMessage(AiChatMessage aiChatMessage);

    /**
     * 删除AI聊天消息
     *
     * @param id AI聊天消息主键
     * @return 结果
     */
    public int deleteAiChatMessageById(Long id);

    /**
     * 批量删除AI聊天消息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAiChatMessageByIds(Long[] ids);
}

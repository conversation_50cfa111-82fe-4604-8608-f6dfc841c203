package com.example.ai.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.ai.dto.ChatInfoRequest;
import com.example.ai.dto.ChatInfoResponse;
import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.Chat;
import com.example.ai.entity.ChatInfo;
import jakarta.validation.constraints.NotBlank;

import java.util.List;


public interface ChatInfoService extends IService<ChatInfo> {

    /**
     * 根据会话id获取会话详细列表信息
     * @param chatId
     * @return
     */
    List<ChatInfoResponse> getByChatId(String chatId);

    /**
     * 保存会话详情
     * @param chatInfoRequest
     */
    void saveChat(ChatInfoRequest chatInfoRequest);

    List<ChatInfoResponse> getByChatIdAndType(String chatId);
}

package com.example.ai.service.impl;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.ai.dto.ChatInfoRequest;
import com.example.ai.dto.ChatInfoResponse;
import com.example.ai.dto.ChatRequest;
import com.example.ai.dto.ChatResponse;
import com.example.ai.entity.Chat;
import com.example.ai.entity.ChatInfo;
import com.example.ai.mapper.ChatInfoMapper;
import com.example.ai.mapper.ChatMapper;
import com.example.ai.service.ChatInfoService;
import com.example.ai.service.ChatService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ChatInfoServiceImpl extends ServiceImpl<ChatInfoMapper, ChatInfo> implements ChatInfoService {
    @Resource
    private ChatInfoMapper chatInfoMapper;


    @Override
    public List<ChatInfoResponse> getByChatId(String chatId) {
        List<ChatInfoResponse> chatInfoResponseList = chatInfoMapper.getByChatId(chatId);
        return chatInfoResponseList;
    }

    @Override
    public void saveChat(ChatInfoRequest chatInfoRequest) {

        //持久化保存对话内容(AI)
        ChatInfo chatInfo = new ChatInfo()
                .setChatId(chatInfoRequest.getChatId())
                .setAiResponse(chatInfoRequest.getAiResponse())
                .setChatInfoContent(chatInfoRequest.getChatInfoContent())
                .setType("ai");


        //持久化保存对话内容(user)
        ChatInfo chatInfoUser = new ChatInfo()
                .setChatId(chatInfoRequest.getChatId())
                .setChatInfoTitle(chatInfoRequest.getChatInfoTitle())
                .setChatInfoContent(chatInfoRequest.getChatInfoTitle())
                .setType("user");


        chatInfoMapper.insert(chatInfoUser);
        chatInfoMapper.insert(chatInfo);


    }

    @Override
    public List<ChatInfoResponse> getByChatIdAndType(String chatId) {
        return chatInfoMapper.getByChatIdAndType(chatId);
    }
}

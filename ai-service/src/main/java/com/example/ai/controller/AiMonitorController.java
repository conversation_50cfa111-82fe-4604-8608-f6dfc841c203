package com.example.ai.controller;

import com.example.ai.service.impl.AiServiceImpl;
import com.example.ai.threadpool.manager.ThreadPoolManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * AI服务监控控制器
 * 提供线程池状态监控等功能
 */
@Slf4j
@RestController
@RequestMapping("/ai/monitor")
@RequiredArgsConstructor
public class AiMonitorController {

    private final AiServiceImpl aiService;
    private final ThreadPoolManager threadPoolManager;

    /**
     * 获取线程池状态
     */
    @GetMapping("/thread-pool")
    public Map<String, Object> getThreadPoolStatus() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", threadPoolManager.getThreadPoolStatus());
            result.put("timestamp", System.currentTimeMillis());
            return result;
        } catch (Exception e) {
            log.error("获取线程池状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            return result;
        }
    }

    /**
     * 获取线程池健康状态
     */
    @GetMapping("/thread-pool/health")
    public Map<String, Object> getThreadPoolHealth() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("health", threadPoolManager.getHealthStatus());
            result.put("timestamp", System.currentTimeMillis());
            return result;
        } catch (Exception e) {
            log.error("获取线程池健康状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("error", e.getMessage());
            result.put("timestamp", System.currentTimeMillis());
            return result;
        }
    }

    /**
     * 获取服务健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> threadPoolStatus = threadPoolManager.getThreadPoolStatus();
            String threadPoolHealth = threadPoolManager.getHealthStatus();

            // 判断整体健康状态
            boolean isHealthy = "HEALTHY".equals(threadPoolHealth);

            Map<String, Object> healthData = new HashMap<>();
            healthData.put("service", "AI Service");
            healthData.put("status", isHealthy ? "UP" : "DOWN");
            healthData.put("threadPool", threadPoolStatus);
            healthData.put("threadPoolHealth", threadPoolHealth);
            healthData.put("timestamp", System.currentTimeMillis());

            result.put("success", true);
            result.put("healthy", isHealthy);
            result.put("data", healthData);

            if (!isHealthy) {
                result.put("message", threadPoolHealth);
            }

        } catch (Exception e) {
            log.error("获取健康状态失败", e);
            result.put("success", false);
            result.put("healthy", false);
            result.put("error", e.getMessage());
        }
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }


}
